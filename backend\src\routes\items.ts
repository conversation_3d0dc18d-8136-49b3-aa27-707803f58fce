import express = require("express");
const router = express.Router();
const { pool } = require("../db/initDB");
import type { ItemDynamic } from "../models/Item";

const items: ItemDynamic[] = [
  {
    id: 4151,
    name: "Abyssal whip",
    members: true,
    limit: 70,
    value: 120001,
    highalch: 72000,
    lowalch: 48000,
    icon: "Abyssal whip.png",
    current: { price: 120001, trend: "positive" },
    today: { price: 120001, trend: "positive" },
    volume: 512
  },
  {
    id: 4587,
    name: "Dragon scimitar",
    members: true,
    limit: 1,
    value: 100000,
    highalch: 72000,
    lowalch: 48000,
    icon: "Dragon scimitar.png",
    current: { price: 100000, trend: "negative" },
    today: { price: 100000, trend: "negative" },
    volume: 128
  }
];

router.get("/", async (req: express.Request, res: express.Response) => {
  try {
    const result = await pool.query("SELECT * FROM items");
    res.json(result.rows);
  } catch (err) {
    console.error("Erro ao buscar items:", err);
    res.status(500).json({ error: "Erro ao buscar items" });
  }
});

router.get("/prices", async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT i.id, i.name, p.current_price, p.current_trend, p.volume, p.today_price, p.today_trend, p.fetched_at
      FROM item_prices p
      JOIN items i ON i.id = p.item_id
    `);
    res.json(result.rows);
  } catch (err) {
    console.error("Erro ao buscar preços:", err);
    res.status(500).json({ error: "Erro ao buscar preços" });
  }
});

router.get("/search", async (req: express.Request, res: express.Response) => {
   try {
    const { name } = req.query;

    if (!name || typeof name !== "string") {
      return res.status(400).json({ error: "Query param 'name' é obrigatório" });
    }

    // Busca com case-insensitive
    const result = await pool.query(
      `
      SELECT 
        i.id,
        i.name,
        i.members,
        i.max_limit,
        i.value,
        i.highalch,
        i.icon,
        p.current_price,
        p.current_trend,
        p.volume,
        p.today_price,
        p.today_trend,
        p.fetched_at
      FROM items i
      LEFT JOIN item_prices p ON i.id = p.item_id
      WHERE LOWER(i.name) LIKE LOWER($1)
      ORDER BY i.name ASC
      `,
      [`%${name}%`]
    );

    return res.json(result.rows);
  } catch (err) {
    console.error("❌ Erro ao buscar itens:", err);
    return res.status(500).json({ error: "Erro interno do servidor" });
  }
});
module.exports = router;